package com.qmqb.imp.job.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.enums.ZtTaskStatusEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.ZtTask;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.ZtTaskMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 禅道任务预警
 * @date 2025/7/7 17:28
 */
@Component
@Slf4j
public class TaskWarmService {

    @Resource
    private ZtTaskMapper taskMapper;

    @Resource
    private IMessageService messageService;

    @Resource
    private DingTalkConfig dingTalkConfig;

    @Resource
    private ISysUserService sysUserService;

    @TraceId("任务状态预警")
    @XxlJob("taskStatusWarnJobHandler")
    public ReturnT<String> taskStatusWarnJobHandler(String param) {
        try {

            XxlJobLogger.log("开始执行任务状态预警定时任务...");
            log.info("开始执行进行任务状态预警定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }

            Date nowDateStart = DateUtils.dateToStart(DateUtils.getNowDate());
            List<SysUser> sysUsers = sysUserService.selectAllUser();
            List<String> usernameList = sysUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());


            Long waitCount = CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectCount(new LambdaQueryWrapper<ZtTask>()
                .eq(ZtTask::getStatus, ZtTaskStatusEnum.WAIT.getValue())
                .eq(ZtTask::getIsParent, 0)
                .in(ZtTask::getAssignedTo, usernameList)
                .lt(ZtTask::getOpenedDate, DateUtils.plusDay(nowDateStart, -5)));


            Long doingCount = CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectCount(new LambdaQueryWrapper<ZtTask>()
                .eq(ZtTask::getStatus, ZtTaskStatusEnum.DOING.getValue())
                .eq(ZtTask::getIsParent, 0)
                .in(ZtTask::getAssignedTo, usernameList)
                .isNull(ZtTask::getActivatedDate)
                .lt(ZtTask::getRealStarted, DateUtils.plusDay(nowDateStart, -10)));

            doingCount += (CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectCount(new LambdaQueryWrapper<ZtTask>()
                .eq(ZtTask::getStatus, ZtTaskStatusEnum.DOING.getValue())
                .eq(ZtTask::getIsParent, 0)
                .in(ZtTask::getAssignedTo, usernameList)
                .lt(ZtTask::getActivatedDate, DateUtils.plusDay(nowDateStart, -10))));


            Long doneCount = CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectCount(new LambdaQueryWrapper<ZtTask>()
                .eq(ZtTask::getStatus, ZtTaskStatusEnum.DONE.getValue())
                .eq(ZtTask::getIsParent, 0)
                .in(ZtTask::getAssignedTo, usernameList)
                .lt(ZtTask::getFinishedDate, DateUtils.plusDay(nowDateStart, -5)));

            Long pauseCount = CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectPauseTimeoutCount(DateUtils.plusDay(nowDateStart, -15), usernameList);
            StringBuilder content = new StringBuilder();
            if (waitCount + doingCount + doneCount + pauseCount > 0) {
                if (waitCount > 0) {
                    content.append(String.format("\n有%d个状态为【需求中】的任务，已超过5天没处理，请及时进行处理，该暂停及时暂停，该取消及时取消。", waitCount));
                }
                if (doingCount > 0) {
                    content.append(String.format("\n有%d个状态为【进行中】的任务，已超过10天没完成，请提高工作效率进行处理。", doingCount));
                }
                if (doneCount > 0) {
                    content.append(String.format("\n有%d个状态为【已完成】的任务，已超过5天没关闭，请及时关闭。", doneCount));
                }
                if (pauseCount > 0) {
                    content.append(String.format("\n有%d个状态为【已暂停】的任务，已超过15天没取消，请及时取消。", pauseCount));
                }
                Map<String, String> map = new HashMap<>(10);
                map.put("content", content.toString());
                send(dingTalkConfig.getJszxRobotUrl(), Constants.TASK_STATUS_TIMEOUT_TAG, map);
            } else {
                send(dingTalkConfig.getJszxRobotUrl(), Constants.TASK_STATUS_NOT_TIMEOUT_TAG, Collections.emptyMap());
            }
            sw.stop();
            XxlJobLogger.log("任务状态预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("任务状态预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("任务状态预警定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 发送预警
     *
     * @param robotUrl 机器人url
     * @param template 模板
     * @param map      数据
     */
    private void send(String robotUrl, String template, Map<String, String> map) {
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }

}

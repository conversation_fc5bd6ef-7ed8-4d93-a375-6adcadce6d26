package com.qmqb.imp.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.qmqb.imp.job.service.ProjectBrandSycService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private ProjectBrandSycService projectBrandSycService;

    @GetMapping
    @SaIgnore
    public void test(){
        projectBrandSycService.syncProjectBrandJobHandler(null);
    }
}
